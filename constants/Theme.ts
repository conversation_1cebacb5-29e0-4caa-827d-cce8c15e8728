export const Colors = {
  // Dark theme colors to match wireframes
  background: '#1a1a1a',
  surface: '#2a2a2a',
  surfaceVariant: '#3a3a3a',
  
  // Primary green colors
  primary: '#4ade80', // Green-400
  primaryDark: '#22c55e', // Green-500
  primaryLight: '#86efac', // Green-300
  
  // Text colors
  text: '#ffffff',
  textSecondary: '#a1a1aa', // Zinc-400
  textMuted: '#71717a', // Zinc-500
  
  // Status colors
  success: '#22c55e',
  warning: '#f59e0b',
  error: '#ef4444',
  
  // Card and component colors
  card: '#2a2a2a',
  cardBorder: '#3f3f46',
  
  // Health status colors
  healthGood: '#22c55e',
  healthWarning: '#f59e0b',
  healthBad: '#ef4444',
  
  // Transparent overlays
  overlay: 'rgba(0, 0, 0, 0.7)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const BorderRadius = {
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

export const Typography = {
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  weights: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
};

export const Shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
};
