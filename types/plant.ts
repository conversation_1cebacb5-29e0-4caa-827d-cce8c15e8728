export interface Plant {
  id: string;
  name: string;
  scientificName: string;
  confidence?: number;
  family?: string;
  commonNames?: string[];
  image: string;
  careRequirements?: {
    light: string;
    water: string;
    temperature: string;
  };
}

export interface PlantInCollection extends Plant {
  nickname: string;
  health: number;
  lastWateredDate: Date;
  status: 'healthy' | 'needs-attention';
  wateringFrequencyDays?: number;
  notes?: string;
  category?: 'indoor' | 'outdoor' | 'flowering';
}

export interface RecentScan {
  id: string;
  name: string;
  confidence: number;
  date: string;
  image: string;
}

export interface DiagnosisResult {
  issue: string;
  severity: 'Low' | 'Medium' | 'High';
  advice: string;
  symptoms: string[];
  photo?: string;
  remarks?: string;
}

export type Screen = 'home' | 'camera' | 'results' | 'collection' | 'profile' | 'diagnose';
