import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  TextInput,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Spacing, BorderRadius, Typography, Shadows } from '@/constants/Theme';
import { mockPlantsInCollection } from '@/data/mockData';
import { PlantInCollection } from '@/types/plant';

export default function CollectionScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'indoor' | 'outdoor' | 'flowering'>('all');

  const categories = [
    { key: 'all', label: 'All Plants' },
    { key: 'indoor', label: 'Indoor' },
    { key: 'outdoor', label: 'Outdoor' },
    { key: 'flowering', label: 'Flowering' },
  ];

  const filteredPlants = mockPlantsInCollection.filter((plant) => {
    const matchesSearch = plant.nickname.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         plant.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || plant.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handlePlantPress = (plant: PlantInCollection) => {
    router.push({
      pathname: '/plant-detail',
      params: {
        plantData: JSON.stringify(plant),
      },
    });
  };

  const getHealthColor = (health: number) => {
    if (health >= 80) return Colors.success;
    if (health >= 60) return Colors.warning;
    return Colors.error;
  };

  const getDaysUntilWatering = (lastWatered: Date, frequency: number) => {
    const today = new Date();
    const daysSinceWatered = Math.floor((today.getTime() - lastWatered.getTime()) / (1000 * 60 * 60 * 24));
    const daysUntilNext = frequency - daysSinceWatered;
    return Math.max(0, daysUntilNext);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>My Plants</Text>
        <TouchableOpacity style={styles.addButton} onPress={() => router.push('/(tabs)/scan')}>
          <IconSymbol name="plus" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <IconSymbol name="magnifyingglass" size={20} color={Colors.textMuted} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search plants..."
            placeholderTextColor={Colors.textMuted}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Category Filter */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryContainer}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.key}
            style={[
              styles.categoryButton,
              selectedCategory === category.key && styles.categoryButtonActive,
            ]}
            onPress={() => setSelectedCategory(category.key as any)}
          >
            <Text
              style={[
                styles.categoryText,
                selectedCategory === category.key && styles.categoryTextActive,
              ]}
            >
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Plants List */}
      <ScrollView style={styles.plantsList} showsVerticalScrollIndicator={false}>
        {filteredPlants.map((plant) => {
          const daysUntilWatering = getDaysUntilWatering(plant.lastWateredDate, plant.wateringFrequencyDays || 7);
          
          return (
            <TouchableOpacity
              key={plant.id}
              style={styles.plantCard}
              onPress={() => handlePlantPress(plant)}
            >
              <Image source={{ uri: plant.image }} style={styles.plantImage} />
              
              <View style={styles.plantInfo}>
                <View style={styles.plantHeader}>
                  <Text style={styles.plantNickname}>{plant.nickname}</Text>
                  <View style={[styles.statusIndicator, { 
                    backgroundColor: plant.status === 'healthy' ? Colors.success : Colors.warning 
                  }]} />
                </View>
                
                <Text style={styles.plantName}>{plant.name}</Text>
                
                <View style={styles.healthContainer}>
                  <Text style={styles.healthLabel}>Health</Text>
                  <View style={styles.healthBarContainer}>
                    <View style={[styles.healthBar, { 
                      width: `${plant.health}%`,
                      backgroundColor: getHealthColor(plant.health)
                    }]} />
                  </View>
                  <Text style={[styles.healthText, { color: getHealthColor(plant.health) }]}>
                    {plant.health}%
                  </Text>
                </View>
                
                <View style={styles.wateringInfo}>
                  <IconSymbol name="drop.fill" size={16} color={Colors.primary} />
                  <Text style={styles.wateringText}>
                    {daysUntilWatering === 0 ? 'Water today' : `Water in ${daysUntilWatering} days`}
                  </Text>
                </View>
              </View>
              
              <TouchableOpacity style={styles.moreButton}>
                <IconSymbol name="ellipsis" size={20} color={Colors.textMuted} />
              </TouchableOpacity>
            </TouchableOpacity>
          );
        })}
        
        {filteredPlants.length === 0 && (
          <View style={styles.emptyState}>
            <IconSymbol name="leaf.fill" size={64} color={Colors.textMuted} />
            <Text style={styles.emptyTitle}>No plants found</Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery ? 'Try adjusting your search' : 'Add your first plant to get started'}
            </Text>
            <TouchableOpacity style={styles.addFirstPlantButton} onPress={() => router.push('/(tabs)/scan')}>
              <Text style={styles.addFirstPlantText}>Add Plant</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.sizes['3xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: Typography.sizes.base,
    color: Colors.text,
  },
  categoryContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  categoryButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.surface,
    marginRight: Spacing.sm,
  },
  categoryButtonActive: {
    backgroundColor: Colors.primary,
  },
  categoryText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.textSecondary,
  },
  categoryTextActive: {
    color: Colors.background,
  },
  plantsList: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  plantCard: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    alignItems: 'center',
    ...Shadows.sm,
  },
  plantImage: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.md,
  },
  plantInfo: {
    flex: 1,
  },
  plantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  plantNickname: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: BorderRadius.full,
  },
  plantName: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  healthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    gap: Spacing.sm,
  },
  healthLabel: {
    fontSize: Typography.sizes.xs,
    color: Colors.textMuted,
    width: 40,
  },
  healthBarContainer: {
    flex: 1,
    height: 6,
    backgroundColor: Colors.surfaceVariant,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  healthBar: {
    height: '100%',
  },
  healthText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.medium,
    width: 35,
    textAlign: 'right',
  },
  wateringInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  wateringText: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
  },
  moreButton: {
    padding: Spacing.sm,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.xxl,
  },
  emptyTitle: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  emptySubtitle: {
    fontSize: Typography.sizes.base,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  addFirstPlantButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  addFirstPlantText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.background,
  },
});
