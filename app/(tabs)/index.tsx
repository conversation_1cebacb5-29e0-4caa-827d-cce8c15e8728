import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Spacing, BorderRadius, Typography, Shadows } from '@/constants/Theme';
import { mockRecentScans, mockPlantsInCollection } from '@/data/mockData';

export default function HomeScreen() {
  const handleScanPress = () => {
    router.push('/(tabs)/scan');
  };

  const handleDiagnosePress = () => {
    router.push('/diagnose');
  };

  const handleViewAllScans = () => {
    router.push('/recent-scans');
  };

  const handleViewAllPlants = () => {
    router.push('/(tabs)/collection');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.greeting}>Good morning!</Text>
          <Text style={styles.subtitle}>Let's take care of your plants</Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionCard} onPress={handleScanPress}>
              <View style={styles.actionIcon}>
                <IconSymbol name="camera.fill" size={24} color={Colors.primary} />
              </View>
              <Text style={styles.actionTitle}>Identify Plant</Text>
              <Text style={styles.actionSubtitle}>Take a photo to identify</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionCard} onPress={handleDiagnosePress}>
              <View style={styles.actionIcon}>
                <IconSymbol name="stethoscope" size={24} color={Colors.primary} />
              </View>
              <Text style={styles.actionTitle}>Diagnose Issue</Text>
              <Text style={styles.actionSubtitle}>Check plant health</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Scans */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Scans</Text>
            <TouchableOpacity onPress={handleViewAllScans}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
            {mockRecentScans.map((scan) => (
              <TouchableOpacity key={scan.id} style={styles.scanCard}>
                <Image source={{ uri: scan.image }} style={styles.scanImage} />
                <Text style={styles.scanName}>{scan.name}</Text>
                <Text style={styles.scanConfidence}>{scan.confidence}% match</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* My Plants Preview */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>My Plants</Text>
            <TouchableOpacity onPress={handleViewAllPlants}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          {mockPlantsInCollection.slice(0, 3).map((plant) => (
            <TouchableOpacity key={plant.id} style={styles.plantCard}>
              <Image source={{ uri: plant.image }} style={styles.plantImage} />
              <View style={styles.plantInfo}>
                <Text style={styles.plantName}>{plant.nickname}</Text>
                <Text style={styles.plantScientific}>{plant.scientificName}</Text>
                <View style={styles.healthContainer}>
                  <View style={[styles.healthBar, { width: `${plant.health}%` }]} />
                </View>
              </View>
              <View style={[styles.statusIndicator, {
                backgroundColor: plant.status === 'healthy' ? Colors.success : Colors.warning
              }]} />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: Spacing.lg,
    paddingTop: Spacing.xl,
  },
  greeting: {
    fontSize: Typography.sizes['3xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.sizes.base,
    color: Colors.textSecondary,
  },
  section: {
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  viewAllText: {
    fontSize: Typography.sizes.sm,
    color: Colors.primary,
    fontWeight: Typography.weights.medium,
  },
  quickActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  actionCard: {
    flex: 1,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    alignItems: 'center',
    ...Shadows.md,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  actionTitle: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.xs,
    textAlign: 'center',
  },
  actionSubtitle: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  horizontalScroll: {
    marginHorizontal: -Spacing.lg,
    paddingHorizontal: Spacing.lg,
  },
  scanCard: {
    width: 120,
    marginRight: Spacing.md,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    ...Shadows.sm,
  },
  scanImage: {
    width: '100%',
    height: 80,
    borderRadius: BorderRadius.sm,
    marginBottom: Spacing.sm,
  },
  scanName: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  scanConfidence: {
    fontSize: Typography.sizes.xs,
    color: Colors.primary,
  },
  plantCard: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    alignItems: 'center',
    ...Shadows.sm,
  },
  plantImage: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.md,
  },
  plantInfo: {
    flex: 1,
  },
  plantName: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  plantScientific: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  healthContainer: {
    height: 4,
    backgroundColor: Colors.surfaceVariant,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  healthBar: {
    height: '100%',
    backgroundColor: Colors.primary,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: BorderRadius.full,
    marginLeft: Spacing.sm,
  },
});
