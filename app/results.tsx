import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Spacing, BorderRadius, Typography, Shadows } from '@/constants/Theme';
import { Plant } from '@/types/plant';

export default function ResultsScreen() {
  const params = useLocalSearchParams();
  const [isAddedToCollection, setIsAddedToCollection] = useState(false);
  
  // Parse the plant data from navigation params
  const plantData: Plant = params.plantData ? JSON.parse(params.plantData as string) : null;
  const imageUri = params.imageUri as string;

  if (!plantData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol name="exclamationmark.triangle" size={64} color={Colors.error} />
          <Text style={styles.errorTitle}>No Results Found</Text>
          <Text style={styles.errorText}>Unable to identify the plant. Please try again.</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => router.back()}>
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const handleAddToCollection = () => {
    setIsAddedToCollection(true);
    Alert.alert(
      'Added to Collection',
      `${plantData.name} has been added to your plant collection!`,
      [
        { text: 'View Collection', onPress: () => router.push('/(tabs)/collection') },
        { text: 'OK', style: 'default' },
      ]
    );
  };

  const handleShareResult = () => {
    Alert.alert('Share', 'Sharing functionality coming soon!');
  };

  const handleLearnMore = () => {
    Alert.alert('Learn More', 'Detailed plant information coming soon!');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <IconSymbol name="chevron.left" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Plant Identified</Text>
        <TouchableOpacity style={styles.shareButton} onPress={handleShareResult}>
          <IconSymbol name="square.and.arrow.up" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Plant Image */}
        <View style={styles.imageContainer}>
          <Image source={{ uri: imageUri || plantData.image }} style={styles.plantImage} />
          <View style={styles.confidenceContainer}>
            <IconSymbol name="checkmark.circle.fill" size={20} color={Colors.success} />
            <Text style={styles.confidenceText}>{plantData.confidence}% Match</Text>
          </View>
        </View>

        {/* Plant Information */}
        <View style={styles.infoContainer}>
          <Text style={styles.plantName}>{plantData.name}</Text>
          <Text style={styles.scientificName}>{plantData.scientificName}</Text>
          
          {plantData.family && (
            <View style={styles.familyContainer}>
              <Text style={styles.familyLabel}>Family:</Text>
              <Text style={styles.familyText}>{plantData.family}</Text>
            </View>
          )}

          {plantData.commonNames && plantData.commonNames.length > 0 && (
            <View style={styles.commonNamesContainer}>
              <Text style={styles.commonNamesLabel}>Common Names:</Text>
              <Text style={styles.commonNamesText}>{plantData.commonNames.join(', ')}</Text>
            </View>
          )}
        </View>

        {/* Care Requirements */}
        {plantData.careRequirements && (
          <View style={styles.careContainer}>
            <Text style={styles.sectionTitle}>Care Requirements</Text>
            
            <View style={styles.careItem}>
              <View style={styles.careIcon}>
                <IconSymbol name="sun.max.fill" size={20} color={Colors.warning} />
              </View>
              <View style={styles.careText}>
                <Text style={styles.careLabel}>Light</Text>
                <Text style={styles.careValue}>{plantData.careRequirements.light}</Text>
              </View>
            </View>

            <View style={styles.careItem}>
              <View style={styles.careIcon}>
                <IconSymbol name="drop.fill" size={20} color={Colors.primary} />
              </View>
              <View style={styles.careText}>
                <Text style={styles.careLabel}>Water</Text>
                <Text style={styles.careValue}>{plantData.careRequirements.water}</Text>
              </View>
            </View>

            <View style={styles.careItem}>
              <View style={styles.careIcon}>
                <IconSymbol name="thermometer" size={20} color={Colors.error} />
              </View>
              <View style={styles.careText}>
                <Text style={styles.careLabel}>Temperature</Text>
                <Text style={styles.careValue}>{plantData.careRequirements.temperature}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity 
            style={[styles.actionButton, styles.primaryButton, isAddedToCollection && styles.disabledButton]} 
            onPress={handleAddToCollection}
            disabled={isAddedToCollection}
          >
            <IconSymbol 
              name={isAddedToCollection ? "checkmark" : "plus"} 
              size={20} 
              color={isAddedToCollection ? Colors.success : Colors.background} 
            />
            <Text style={[styles.actionButtonText, isAddedToCollection && styles.disabledButtonText]}>
              {isAddedToCollection ? 'Added to Collection' : 'Add to Collection'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionButton, styles.secondaryButton]} onPress={handleLearnMore}>
            <IconSymbol name="book.fill" size={20} color={Colors.primary} />
            <Text style={styles.secondaryButtonText}>Learn More</Text>
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity 
            style={styles.quickAction} 
            onPress={() => router.push('/(tabs)/scan')}
          >
            <IconSymbol name="camera.fill" size={24} color={Colors.primary} />
            <Text style={styles.quickActionText}>Scan Another</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.quickAction}
            onPress={() => router.push('/diagnose')}
          >
            <IconSymbol name="stethoscope" size={24} color={Colors.primary} />
            <Text style={styles.quickActionText}>Diagnose Issues</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.quickAction}
            onPress={() => router.push('/(tabs)/collection')}
          >
            <IconSymbol name="leaf.fill" size={24} color={Colors.primary} />
            <Text style={styles.quickActionText}>My Plants</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
  },
  shareButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    margin: Spacing.lg,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    ...Shadows.lg,
  },
  plantImage: {
    width: '100%',
    height: 300,
  },
  confidenceContainer: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.overlay,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
    gap: Spacing.xs,
  },
  confidenceText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
  },
  infoContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  plantName: {
    fontSize: Typography.sizes['3xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  scientificName: {
    fontSize: Typography.sizes.lg,
    fontStyle: 'italic',
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
  },
  familyContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.sm,
  },
  familyLabel: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.medium,
    color: Colors.textMuted,
    marginRight: Spacing.sm,
  },
  familyText: {
    fontSize: Typography.sizes.base,
    color: Colors.text,
  },
  commonNamesContainer: {
    marginTop: Spacing.sm,
  },
  commonNamesLabel: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.medium,
    color: Colors.textMuted,
    marginBottom: Spacing.xs,
  },
  commonNamesText: {
    fontSize: Typography.sizes.base,
    color: Colors.text,
  },
  careContainer: {
    backgroundColor: Colors.surface,
    margin: Spacing.lg,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    ...Shadows.sm,
  },
  sectionTitle: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.lg,
  },
  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  careIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  careText: {
    flex: 1,
  },
  careLabel: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.textMuted,
    marginBottom: Spacing.xs,
  },
  careValue: {
    fontSize: Typography.sizes.base,
    color: Colors.text,
  },
  actionsContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
    gap: Spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  disabledButton: {
    backgroundColor: Colors.surfaceVariant,
  },
  actionButtonText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.background,
  },
  secondaryButtonText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.primary,
  },
  disabledButtonText: {
    color: Colors.success,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
    gap: Spacing.md,
  },
  quickAction: {
    flex: 1,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    alignItems: 'center',
    ...Shadows.sm,
  },
  quickActionText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorTitle: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  errorText: {
    fontSize: Typography.sizes.base,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  retryButtonText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.background,
  },
});
