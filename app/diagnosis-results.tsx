import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Spacing, BorderRadius, Typography, Shadows } from '@/constants/Theme';
import { DiagnosisResult } from '@/types/plant';

export default function DiagnosisResultsScreen() {
  const params = useLocalSearchParams();
  const [expandedTreatment, setExpandedTreatment] = useState<number | null>(null);
  
  // Parse the diagnosis data from navigation params
  const diagnosisData: DiagnosisResult = params.diagnosisData ? JSON.parse(params.diagnosisData as string) : null;
  const imageUri = params.imageUri as string;

  if (!diagnosisData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol name="exclamationmark.triangle" size={64} color={Colors.error} />
          <Text style={styles.errorTitle}>No Diagnosis Found</Text>
          <Text style={styles.errorText}>Unable to diagnose the plant issue. Please try again.</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => router.back()}>
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'low':
        return Colors.success;
      case 'medium':
        return Colors.warning;
      case 'high':
        return Colors.error;
      default:
        return Colors.textMuted;
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'low':
        return 'checkmark.circle.fill';
      case 'medium':
        return 'exclamationmark.triangle.fill';
      case 'high':
        return 'xmark.circle.fill';
      default:
        return 'questionmark.circle.fill';
    }
  };

  const handleShareResult = () => {
    Alert.alert('Share', 'Sharing functionality coming soon!');
  };

  const toggleTreatment = (index: number) => {
    setExpandedTreatment(expandedTreatment === index ? null : index);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <IconSymbol name="chevron.left" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Diagnosis Results</Text>
        <TouchableOpacity style={styles.shareButton} onPress={handleShareResult}>
          <IconSymbol name="square.and.arrow.up" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Plant Image */}
        <View style={styles.imageContainer}>
          <Image source={{ uri: imageUri }} style={styles.plantImage} />
          <View style={styles.confidenceContainer}>
            <IconSymbol name="checkmark.circle.fill" size={20} color={Colors.success} />
            <Text style={styles.confidenceText}>{diagnosisData.confidence}% Confidence</Text>
          </View>
        </View>

        {/* Diagnosis Information */}
        <View style={styles.diagnosisContainer}>
          <View style={styles.severityContainer}>
            <IconSymbol 
              name={getSeverityIcon(diagnosisData.severity)} 
              size={24} 
              color={getSeverityColor(diagnosisData.severity)} 
            />
            <Text style={[styles.severityText, { color: getSeverityColor(diagnosisData.severity) }]}>
              {diagnosisData.severity.toUpperCase()} SEVERITY
            </Text>
          </View>

          <Text style={styles.issueTitle}>{diagnosisData.issue}</Text>
          <Text style={styles.issueDescription}>{diagnosisData.description}</Text>

          {diagnosisData.causes && diagnosisData.causes.length > 0 && (
            <View style={styles.causesContainer}>
              <Text style={styles.sectionTitle}>Possible Causes</Text>
              {diagnosisData.causes.map((cause, index) => (
                <View key={index} style={styles.causeItem}>
                  <IconSymbol name="circle.fill" size={8} color={Colors.primary} />
                  <Text style={styles.causeText}>{cause}</Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Treatment Recommendations */}
        {diagnosisData.treatments && diagnosisData.treatments.length > 0 && (
          <View style={styles.treatmentsContainer}>
            <Text style={styles.sectionTitle}>Treatment Recommendations</Text>
            {diagnosisData.treatments.map((treatment, index) => (
              <View key={index} style={styles.treatmentCard}>
                <TouchableOpacity 
                  style={styles.treatmentHeader}
                  onPress={() => toggleTreatment(index)}
                >
                  <View style={styles.treatmentTitleContainer}>
                    <IconSymbol name="leaf.fill" size={20} color={Colors.primary} />
                    <Text style={styles.treatmentTitle}>{treatment.title}</Text>
                  </View>
                  <IconSymbol 
                    name={expandedTreatment === index ? "chevron.up" : "chevron.down"} 
                    size={16} 
                    color={Colors.textMuted} 
                  />
                </TouchableOpacity>
                
                {expandedTreatment === index && (
                  <View style={styles.treatmentContent}>
                    <Text style={styles.treatmentDescription}>{treatment.description}</Text>
                    
                    {treatment.steps && treatment.steps.length > 0 && (
                      <View style={styles.stepsContainer}>
                        <Text style={styles.stepsTitle}>Steps:</Text>
                        {treatment.steps.map((step, stepIndex) => (
                          <View key={stepIndex} style={styles.stepItem}>
                            <View style={styles.stepNumber}>
                              <Text style={styles.stepNumberText}>{stepIndex + 1}</Text>
                            </View>
                            <Text style={styles.stepText}>{step}</Text>
                          </View>
                        ))}
                      </View>
                    )}
                  </View>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Prevention Tips */}
        {diagnosisData.prevention && diagnosisData.prevention.length > 0 && (
          <View style={styles.preventionContainer}>
            <Text style={styles.sectionTitle}>Prevention Tips</Text>
            {diagnosisData.prevention.map((tip, index) => (
              <View key={index} style={styles.preventionItem}>
                <IconSymbol name="lightbulb.fill" size={16} color={Colors.warning} />
                <Text style={styles.preventionText}>{tip}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity 
            style={styles.quickAction} 
            onPress={() => router.push('/diagnose')}
          >
            <IconSymbol name="stethoscope" size={24} color={Colors.primary} />
            <Text style={styles.quickActionText}>Diagnose Another</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.quickAction}
            onPress={() => router.push('/(tabs)/scan')}
          >
            <IconSymbol name="camera.fill" size={24} color={Colors.primary} />
            <Text style={styles.quickActionText}>Identify Plant</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.quickAction}
            onPress={() => router.push('/(tabs)/collection')}
          >
            <IconSymbol name="leaf.fill" size={24} color={Colors.primary} />
            <Text style={styles.quickActionText}>My Plants</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
  },
  shareButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    margin: Spacing.lg,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    ...Shadows.lg,
  },
  plantImage: {
    width: '100%',
    height: 250,
  },
  confidenceContainer: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.overlay,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
    gap: Spacing.xs,
  },
  confidenceText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
  },
  diagnosisContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  severityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },
  severityText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.bold,
    letterSpacing: 1,
  },
  issueTitle: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  issueDescription: {
    fontSize: Typography.sizes.base,
    color: Colors.textSecondary,
    lineHeight: 24,
    marginBottom: Spacing.lg,
  },
  causesContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    ...Shadows.sm,
  },
  sectionTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  causeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    gap: Spacing.sm,
  },
  causeText: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    flex: 1,
  },
  treatmentsContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  treatmentCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.md,
    overflow: 'hidden',
    ...Shadows.sm,
  },
  treatmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.md,
  },
  treatmentTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: Spacing.sm,
  },
  treatmentTitle: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
  },
  treatmentContent: {
    paddingHorizontal: Spacing.md,
    paddingBottom: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.surfaceVariant,
  },
  treatmentDescription: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  stepsContainer: {
    marginTop: Spacing.sm,
  },
  stepsTitle: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
    gap: Spacing.sm,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    color: Colors.background,
  },
  stepText: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    flex: 1,
    lineHeight: 20,
  },
  preventionContainer: {
    backgroundColor: Colors.surface,
    margin: Spacing.lg,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    ...Shadows.sm,
  },
  preventionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
    gap: Spacing.sm,
  },
  preventionText: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    flex: 1,
    lineHeight: 20,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
    gap: Spacing.md,
  },
  quickAction: {
    flex: 1,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    alignItems: 'center',
    ...Shadows.sm,
  },
  quickActionText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorTitle: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  errorText: {
    fontSize: Typography.sizes.base,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  retryButtonText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.background,
  },
});
