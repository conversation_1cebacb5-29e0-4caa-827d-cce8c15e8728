import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Spacing, BorderRadius, Typography, Shadows } from '@/constants/Theme';
import { PlantInCollection } from '@/types/plant';

export default function PlantDetailScreen() {
  const params = useLocalSearchParams();
  const [isWatered, setIsWatered] = useState(false);
  
  // Parse the plant data from navigation params
  const plantData: PlantInCollection = params.plantData ? JSON.parse(params.plantData as string) : null;

  if (!plantData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol name="exclamationmark.triangle" size={64} color={Colors.error} />
          <Text style={styles.errorTitle}>Plant Not Found</Text>
          <Text style={styles.errorText}>Unable to load plant details.</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => router.back()}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const getHealthColor = (health: number) => {
    if (health >= 80) return Colors.success;
    if (health >= 60) return Colors.warning;
    return Colors.error;
  };

  const getDaysUntilWatering = (lastWatered: Date, frequency: number) => {
    const today = new Date();
    const daysSinceWatered = Math.floor((today.getTime() - lastWatered.getTime()) / (1000 * 60 * 60 * 24));
    const daysUntilNext = frequency - daysSinceWatered;
    return Math.max(0, daysUntilNext);
  };

  const handleWaterPlant = () => {
    setIsWatered(true);
    Alert.alert(
      'Plant Watered!',
      `${plantData.nickname} has been watered. Great job taking care of your plant!`,
      [{ text: 'OK', style: 'default' }]
    );
  };

  const handleEditPlant = () => {
    Alert.alert('Edit Plant', 'Plant editing functionality coming soon!');
  };

  const handleRemovePlant = () => {
    Alert.alert(
      'Remove Plant',
      `Are you sure you want to remove ${plantData.nickname} from your collection?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => {
          Alert.alert('Removed', 'Plant has been removed from your collection');
          router.back();
        }},
      ]
    );
  };

  const daysUntilWatering = getDaysUntilWatering(plantData.lastWateredDate, plantData.wateringFrequencyDays || 7);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <IconSymbol name="chevron.left" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{plantData.nickname}</Text>
        <TouchableOpacity style={styles.editButton} onPress={handleEditPlant}>
          <IconSymbol name="pencil" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Plant Image */}
        <View style={styles.imageContainer}>
          <Image source={{ uri: plantData.image }} style={styles.plantImage} />
          <View style={styles.statusContainer}>
            <View style={[styles.statusIndicator, { 
              backgroundColor: plantData.status === 'healthy' ? Colors.success : Colors.warning 
            }]} />
            <Text style={styles.statusText}>
              {plantData.status === 'healthy' ? 'Healthy' : 'Needs Attention'}
            </Text>
          </View>
        </View>

        {/* Plant Information */}
        <View style={styles.infoContainer}>
          <Text style={styles.plantNickname}>{plantData.nickname}</Text>
          <Text style={styles.plantName}>{plantData.name}</Text>
          <Text style={styles.plantCategory}>{plantData.category} plant</Text>
          
          <Text style={styles.addedDate}>
            Added on {plantData.addedDate.toLocaleDateString()}
          </Text>
        </View>

        {/* Health Status */}
        <View style={styles.healthSection}>
          <Text style={styles.sectionTitle}>Health Status</Text>
          <View style={styles.healthContainer}>
            <View style={styles.healthInfo}>
              <Text style={styles.healthLabel}>Overall Health</Text>
              <View style={styles.healthBarContainer}>
                <View style={[styles.healthBar, { 
                  width: `${plantData.health}%`,
                  backgroundColor: getHealthColor(plantData.health)
                }]} />
              </View>
            </View>
            <Text style={[styles.healthText, { color: getHealthColor(plantData.health) }]}>
              {plantData.health}%
            </Text>
          </View>
        </View>

        {/* Care Schedule */}
        <View style={styles.careSection}>
          <Text style={styles.sectionTitle}>Care Schedule</Text>
          
          <View style={styles.careItem}>
            <View style={styles.careIcon}>
              <IconSymbol name="drop.fill" size={20} color={Colors.primary} />
            </View>
            <View style={styles.careInfo}>
              <Text style={styles.careLabel}>Watering</Text>
              <Text style={styles.careValue}>
                {isWatered ? 'Watered today!' : 
                 daysUntilWatering === 0 ? 'Water today' : 
                 `Water in ${daysUntilWatering} days`}
              </Text>
              <Text style={styles.careFrequency}>
                Every {plantData.wateringFrequencyDays || 7} days
              </Text>
            </View>
            {!isWatered && daysUntilWatering === 0 && (
              <TouchableOpacity style={styles.waterButton} onPress={handleWaterPlant}>
                <IconSymbol name="drop.fill" size={16} color={Colors.background} />
                <Text style={styles.waterButtonText}>Water</Text>
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.careItem}>
            <View style={styles.careIcon}>
              <IconSymbol name="sun.max.fill" size={20} color={Colors.warning} />
            </View>
            <View style={styles.careInfo}>
              <Text style={styles.careLabel}>Light</Text>
              <Text style={styles.careValue}>Bright indirect light</Text>
              <Text style={styles.careFrequency}>6-8 hours daily</Text>
            </View>
          </View>

          <View style={styles.careItem}>
            <View style={styles.careIcon}>
              <IconSymbol name="thermometer" size={20} color={Colors.error} />
            </View>
            <View style={styles.careInfo}>
              <Text style={styles.careLabel}>Temperature</Text>
              <Text style={styles.careValue}>18-24°C (65-75°F)</Text>
              <Text style={styles.careFrequency}>Ideal range</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity style={styles.actionButton} onPress={() => router.push('/diagnose')}>
            <IconSymbol name="stethoscope" size={20} color={Colors.primary} />
            <Text style={styles.actionButtonText}>Diagnose Issues</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleEditPlant}>
            <IconSymbol name="pencil" size={20} color={Colors.primary} />
            <Text style={styles.actionButtonText}>Edit Details</Text>
          </TouchableOpacity>
        </View>

        {/* Remove Plant */}
        <View style={styles.dangerZone}>
          <TouchableOpacity style={styles.removeButton} onPress={handleRemovePlant}>
            <IconSymbol name="trash" size={20} color={Colors.error} />
            <Text style={styles.removeButtonText}>Remove from Collection</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    margin: Spacing.lg,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    ...Shadows.lg,
  },
  plantImage: {
    width: '100%',
    height: 300,
  },
  statusContainer: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.overlay,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
    gap: Spacing.xs,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: BorderRadius.full,
  },
  statusText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
  },
  infoContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  plantNickname: {
    fontSize: Typography.sizes['3xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  plantName: {
    fontSize: Typography.sizes.lg,
    fontStyle: 'italic',
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  plantCategory: {
    fontSize: Typography.sizes.base,
    color: Colors.textMuted,
    textTransform: 'capitalize',
    marginBottom: Spacing.sm,
  },
  addedDate: {
    fontSize: Typography.sizes.sm,
    color: Colors.textMuted,
  },
  healthSection: {
    backgroundColor: Colors.surface,
    margin: Spacing.lg,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    ...Shadows.sm,
  },
  sectionTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  healthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  healthInfo: {
    flex: 1,
  },
  healthLabel: {
    fontSize: Typography.sizes.sm,
    color: Colors.textMuted,
    marginBottom: Spacing.xs,
  },
  healthBarContainer: {
    height: 8,
    backgroundColor: Colors.surfaceVariant,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  healthBar: {
    height: '100%',
  },
  healthText: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.bold,
  },
  careSection: {
    backgroundColor: Colors.surface,
    margin: Spacing.lg,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    ...Shadows.sm,
  },
  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  careIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  careInfo: {
    flex: 1,
  },
  careLabel: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  careValue: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  careFrequency: {
    fontSize: Typography.sizes.xs,
    color: Colors.textMuted,
  },
  waterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    gap: Spacing.xs,
  },
  waterButtonText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.background,
  },
  actionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
    gap: Spacing.md,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    gap: Spacing.sm,
  },
  actionButtonText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.primary,
  },
  dangerZone: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  removeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    gap: Spacing.sm,
  },
  removeButtonText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.error,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorTitle: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  errorText: {
    fontSize: Typography.sizes.base,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  retryButtonText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.background,
  },
});
