import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, Spacing, BorderRadius, Typography, Shadows } from '@/constants/Theme';
import { mockRecentScans } from '@/data/mockData';
import { RecentScan } from '@/types/plant';

export default function RecentScansScreen() {
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'identified' | 'unidentified'>('all');

  const filters = [
    { key: 'all', label: 'All Scans' },
    { key: 'identified', label: 'Identified' },
    { key: 'unidentified', label: 'Unidentified' },
  ];

  const filteredScans = mockRecentScans.filter((scan) => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'identified') return scan.result !== null;
    if (selectedFilter === 'unidentified') return scan.result === null;
    return true;
  });

  const handleScanPress = (scan: RecentScan) => {
    if (scan.result) {
      // Navigate to results screen with the plant data
      router.push({
        pathname: '/results',
        params: {
          plantData: JSON.stringify(scan.result),
          imageUri: scan.image,
        },
      });
    } else {
      Alert.alert(
        'Scan Failed',
        'This scan could not identify the plant. Would you like to try scanning again?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Scan Again', onPress: () => router.push('/(tabs)/scan') },
        ]
      );
    }
  };

  const handleRescan = (scan: RecentScan) => {
    Alert.alert(
      'Rescan Plant',
      'Would you like to scan this plant again?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Rescan', onPress: () => router.push('/(tabs)/scan') },
      ]
    );
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <IconSymbol name="chevron.left" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Recent Scans</Text>
        <TouchableOpacity style={styles.scanButton} onPress={() => router.push('/(tabs)/scan')}>
          <IconSymbol name="camera.fill" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
        {filters.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              selectedFilter === filter.key && styles.filterButtonActive,
            ]}
            onPress={() => setSelectedFilter(filter.key as any)}
          >
            <Text
              style={[
                styles.filterText,
                selectedFilter === filter.key && styles.filterTextActive,
              ]}
            >
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Scans List */}
      <ScrollView style={styles.scansList} showsVerticalScrollIndicator={false}>
        {filteredScans.map((scan) => (
          <TouchableOpacity
            key={scan.id}
            style={styles.scanCard}
            onPress={() => handleScanPress(scan)}
          >
            <Image source={{ uri: scan.image }} style={styles.scanImage} />
            
            <View style={styles.scanInfo}>
              <View style={styles.scanHeader}>
                <Text style={styles.scanTitle}>
                  {scan.result ? scan.result.name : 'Unidentified Plant'}
                </Text>
                <Text style={styles.scanTime}>{getTimeAgo(scan.date)}</Text>
              </View>
              
              {scan.result ? (
                <View style={styles.resultInfo}>
                  <View style={styles.confidenceContainer}>
                    <IconSymbol name="checkmark.circle.fill" size={16} color={Colors.success} />
                    <Text style={styles.confidenceText}>{scan.result.confidence}% match</Text>
                  </View>
                  {scan.result.scientificName && (
                    <Text style={styles.scientificName}>{scan.result.scientificName}</Text>
                  )}
                </View>
              ) : (
                <View style={styles.failedInfo}>
                  <IconSymbol name="xmark.circle.fill" size={16} color={Colors.error} />
                  <Text style={styles.failedText}>Identification failed</Text>
                </View>
              )}
            </View>
            
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => handleRescan(scan)}
            >
              <IconSymbol name="arrow.clockwise" size={20} color={Colors.textMuted} />
            </TouchableOpacity>
          </TouchableOpacity>
        ))}
        
        {filteredScans.length === 0 && (
          <View style={styles.emptyState}>
            <IconSymbol name="camera.fill" size={64} color={Colors.textMuted} />
            <Text style={styles.emptyTitle}>No scans found</Text>
            <Text style={styles.emptySubtitle}>
              {selectedFilter === 'all' 
                ? 'Start scanning plants to see your history here'
                : `No ${selectedFilter} scans found`}
            </Text>
            <TouchableOpacity style={styles.scanNowButton} onPress={() => router.push('/(tabs)/scan')}>
              <Text style={styles.scanNowText}>Scan Plant</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <TouchableOpacity 
          style={styles.quickAction} 
          onPress={() => router.push('/(tabs)/scan')}
        >
          <IconSymbol name="camera.fill" size={24} color={Colors.primary} />
          <Text style={styles.quickActionText}>New Scan</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.quickAction}
          onPress={() => router.push('/diagnose')}
        >
          <IconSymbol name="stethoscope" size={24} color={Colors.primary} />
          <Text style={styles.quickActionText}>Diagnose</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.quickAction}
          onPress={() => router.push('/(tabs)/collection')}
        >
          <IconSymbol name="leaf.fill" size={24} color={Colors.primary} />
          <Text style={styles.quickActionText}>My Plants</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
  },
  scanButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  filterButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.surface,
    marginRight: Spacing.sm,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary,
  },
  filterText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.textSecondary,
  },
  filterTextActive: {
    color: Colors.background,
  },
  scansList: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  scanCard: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    alignItems: 'center',
    ...Shadows.sm,
  },
  scanImage: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.md,
  },
  scanInfo: {
    flex: 1,
  },
  scanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  scanTitle: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    flex: 1,
    marginRight: Spacing.sm,
  },
  scanTime: {
    fontSize: Typography.sizes.xs,
    color: Colors.textMuted,
  },
  resultInfo: {
    gap: Spacing.xs,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  confidenceText: {
    fontSize: Typography.sizes.sm,
    color: Colors.success,
    fontWeight: Typography.weights.medium,
  },
  scientificName: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  failedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  failedText: {
    fontSize: Typography.sizes.sm,
    color: Colors.error,
    fontWeight: Typography.weights.medium,
  },
  actionButton: {
    padding: Spacing.sm,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.xxl,
  },
  emptyTitle: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  emptySubtitle: {
    fontSize: Typography.sizes.base,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  scanNowButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
  },
  scanNowText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.background,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.cardBorder,
    gap: Spacing.md,
  },
  quickAction: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: Spacing.sm,
  },
  quickActionText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
});
